#!/usr/bin/env python3
"""
Enhanced Twitter Monitor - Advanced version with image support, better Discord integration,
and robust multi-account monitoring
"""

import asyncio
import os
import json
import time
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Set, Callable, Union, Awaitable, Optional
import aiohttp
from playwright.async_api import async_playwright, <PERSON>, Browser
from urllib.parse import urlparse
import hashlib
import re

# Configuration
COOKIES_PATH = "cookies.json"
CONFIG_FILE = "monitor_config.json"

class TwitterMonitor:
    """Enhanced Twitter monitor with image support and better Discord integration"""

    def __init__(self):
        self.callbacks = []
        self.monitored_accounts = {}  # Now stores: {username: {'page': page, 'start_time': datetime, 'tweet_count': int}}
        self.page = None  # Main page for login check
        self.browser = None
        self.context = None
        self.playwright = None
        self.is_running = False
        self.known_tweet_ids = set()
        self.session = None

    def add_callback(self, callback):
        """Add a callback for tweet notifications"""
        self.callbacks.append(callback)

    async def setup_browser(self):
        """Set up the Playwright browser with better stealth"""
        print("🚀 Starting enhanced Playwright browser...")
        self.playwright = await async_playwright().start()

        # Launch with better stealth options
        self.browser = await self.playwright.chromium.launch(
            headless=False,
            args=[
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-extensions',
                '--no-first-run',
                '--disable-default-apps'
            ]
        )

        # Enhanced context with better fingerprinting
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            locale='en-US',
            timezone_id='America/New_York',
            permissions=['notifications']
        )

        # Add stealth script
        await self.context.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });

            // Remove automation indicators
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        """)

        # Load cookies if available
        if os.path.exists(COOKIES_PATH):
            print(f"📥 Loading cookies from {COOKIES_PATH}")
            try:
                with open(COOKIES_PATH, 'r') as f:
                    cookies = json.load(f)
                    await self.context.add_cookies(cookies)
                print("✅ Cookies loaded successfully")
            except Exception as e:
                print(f"⚠️ Error loading cookies: {e}")

        # Create the page
        try:
            self.page = await self.context.new_page()
            self.page.set_default_timeout(30000)  # 30 seconds timeout
            return True
        except Exception as e:
            print(f"❌ Error setting up browser: {e}")
            return False

    async def check_login_status(self):
        """Check if we're logged into Twitter"""
        if self.page is None:
            print("❌ Browser page not initialized.")
            return False

        try:
            print("🔍 Checking login status...")
            if self.page:
                try:
                    await self.page.goto("https://twitter.com/home", wait_until="networkidle")

                    # Wait a bit for page to fully load
                    await asyncio.sleep(3)

                    # Check for login indicators
                    try:
                        home_timeline = await self.page.query_selector('[data-testid="primaryColumn"]')
                        login_form = await self.page.query_selector('[data-testid="loginForm"]')
                        login_button = await self.page.query_selector('a[href="/i/flow/login"]')

                        if home_timeline and not login_form and not login_button:
                            print("✅ Successfully logged in!")
                            if self.context:
                                try:
                                    await self.save_cookies()
                                except Exception as e:
                                    print(f"❌ Error saving cookies: {e}")
                            return True
                        else:
                            print("❌ Not logged in - please log in manually in the browser")
                            print("   The browser will stay open for you to log in...")
                            return False
                    except Exception as e:
                        print(f"❌ Error during querySelector: {e}")
                        return False
                except Exception as e:
                    print(f"❌ Error during goto: {e}")
                    return False
            else:
                print("❌ Browser page not initialized.")
                return False

        except Exception as e:
            print(f"❌ Error checking login status: {e}")
            return False

    async def save_cookies(self):
        """Save current cookies"""
        if self.context is None:
            print("❌ Browser context not initialized.")
            return

        try:
            cookies = await self.context.cookies()
            with open(COOKIES_PATH, 'w') as f:
                json.dump(cookies, f, indent=2)
            print("💾 Cookies saved")
        except Exception as e:
            print(f"⚠️ Error saving cookies: {e}")

    async def process_new_tweets(self, new_tweets):
        """Process newly detected tweets"""
        if not new_tweets:
            return

        print(f"\n🆕 Found {len(new_tweets)} new tweet(s)!")

        for tweet in new_tweets:
            try:
                # Update account stats using the monitored_username field
                monitored_username = tweet.get('monitored_username')
                if monitored_username and monitored_username in self.monitored_accounts:
                    self.monitored_accounts[monitored_username]['tweet_count'] += 1

                # Execute callbacks
                for callback in self.callbacks:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(tweet)
                        else:
                            callback(tweet)
                    except Exception as e:
                        print(f"❌ Error in callback: {e}")

            except Exception as e:
                print(f"❌ Error processing tweet: {e}")

    async def inject_enhanced_monitor(self):
        """Inject enhanced tweet monitoring JavaScript"""
        if self.page is None:
            print("❌ Browser page not initialized.")
            return False

        await self.page.evaluate("""() => {
            window.tweetMonitor = {
                seenTweetIds: new Set(),
                currentUsername: null,
                lastScrollTime: Date.now(),

                init() {
                    console.log("🔧 Enhanced tweet monitor initialized");
                },

                setUsername(username) {
                    this.currentUsername = username;
                    console.log(`👀 Now monitoring @${username}`);
                },

                getAllTweets() {
                    // Multiple selectors for better compatibility
                    const selectors = [
                        'article[data-testid="tweet"]',
                        'div[data-testid="cellInnerDiv"] article',
                        '[data-testid="tweet"]'
                    ];

                    for (const selector of selectors) {
                        const elements = document.querySelectorAll(selector);
                        if (elements.length > 0) {
                            return Array.from(elements);
                        }
                    }
                    return [];
                },

                extractTweetData(tweetElement) {
                    try {
                        // Get tweet URL and ID
                        const linkEl = tweetElement.querySelector('a[href*="/status/"]');
                        if (!linkEl) return null;

                        const url = linkEl.href;
                        const idMatch = url.match(/\\/status\\/(\\d+)/);
                        if (!idMatch) return null;
                        const id = idMatch[1];

                        // Get username from URL or link
                        const usernameMatch = url.match(/twitter\\.com\\/([^/]+)\\/status/);
                        const username = usernameMatch ? usernameMatch[1] : this.currentUsername;

                        // Get tweet text
                        let text = "";
                        const textEl = tweetElement.querySelector('div[data-testid="tweetText"]');
                        if (textEl) {
                            text = textEl.innerText.trim();
                        } else {
                            // Fallback text extraction
                            const possibleTextElements = tweetElement.querySelectorAll('div[lang], span[lang]');
                            for (const el of possibleTextElements) {
                                if (el.innerText && el.innerText.trim().length > text.length) {
                                    text = el.innerText.trim();
                                }
                            }
                        }

                        // Get time information
                        let displayTime = "Unknown time";
                        let timestamp = null;
                        const timeEl = tweetElement.querySelector('time');
                        if (timeEl) {
                            displayTime = timeEl.innerText;
                            timestamp = timeEl.getAttribute('datetime');
                        }

                        // Get profile image
                        let profileImage = null;
                        const profileImgEl = tweetElement.querySelector('img[src*="profile_images"]');
                        if (profileImgEl) {
                            profileImage = profileImgEl.src;
                        }

                        // Get tweet images/media
                        const mediaImages = [];
                        const mediaEls = tweetElement.querySelectorAll('img[src*="media"]');
                        mediaEls.forEach(img => {
                            if (img.src && img.src.includes('media')) {
                                // Get larger version of image
                                const largeUrl = img.src.replace(/&name=\\w+/, '&name=large');
                                mediaImages.push(largeUrl);
                            }
                        });

                        // Get engagement metrics
                        const getMetric = (testId) => {
                            const element = tweetElement.querySelector(`[data-testid="${testId}"]`);
                            if (!element) return "0";

                            const textEl = element.querySelector('[data-testid="app-text-transition-container"]') ||
                                          element.querySelector('span');
                            return textEl ? textEl.innerText.trim() || "0" : "0";
                        };

                        const replies = getMetric("reply");
                        const retweets = getMetric("retweet");
                        const likes = getMetric("like");
                        const views = getMetric("views");

                        // Check if this is a retweet
                        const isRetweet = tweetElement.querySelector('[data-testid="socialContext"]') !== null;

                        return {
                            id,
                            text: text || "No text content",
                            url,
                            time: displayTime,
                            timestamp,
                            username,
                            profileImage,
                            mediaImages,
                            replies,
                            retweets,
                            likes,
                            views,
                            isRetweet,
                            detectedAt: new Date().toISOString(),
                            isNew: !this.seenTweetIds.has(id)
                        };
                    } catch (error) {
                        console.error("Error extracting tweet data:", error);
                        return null;
                    }
                },

                getNewTweets() {
                    const tweetElements = this.getAllTweets();
                    const newTweets = [];

                    for (const tweetEl of tweetElements) {
                        const tweetData = this.extractTweetData(tweetEl);

                        if (!tweetData || !tweetData.id) continue;

                        // Only return truly new tweets
                        if (!this.seenTweetIds.has(tweetData.id)) {
                            this.seenTweetIds.add(tweetData.id);
                            newTweets.push(tweetData);
                        }
                    }

                    return newTweets;
                },

                processExistingTweets() {
                    const tweetElements = this.getAllTweets();
                    let count = 0;

                    for (const tweetEl of tweetElements) {
                        const tweetData = this.extractTweetData(tweetEl);
                        if (tweetData && tweetData.id) {
                            this.seenTweetIds.add(tweetData.id);
                            count++;
                        }
                    }

                    return count;
                },

                smartScroll() {
                    const now = Date.now();
                    if (now - this.lastScrollTime < 1000) return false;

                    const actions = [
                        () => window.scrollBy(0, Math.floor(200 + Math.random() * 400)), // Scroll down
                        () => window.scrollBy(0, -Math.floor(100 + Math.random() * 400)), // Scroll up
                        () => window.scrollTo(0, 0), // Go to top
                        () => window.scrollTo(0, Math.floor(window.innerHeight * 0.5)) // Go to middle
                    ];

                    const weights = [0.6, 0.2, 0.15, 0.05]; // Weighted random selection
                    const random = Math.random();
                    let cumulativeWeight = 0;

                    for (let i = 0; i < actions.length; i++) {
                        cumulativeWeight += weights[i];
                        if (random <= cumulativeWeight) {
                            actions[i]();
                            break;
                        }
                    }

                    this.lastScrollTime = now;
                    return true;
                }
            };

            window.tweetMonitor.init();
        }""")

        print("✅ Enhanced tweet monitor JavaScript injected")

    async def inject_enhanced_monitor_on_page(self, page):
        """Inject enhanced tweet monitoring JavaScript on a specific page"""
        if page is None:
            print("❌ Page not initialized.")
            return False

        await page.evaluate("""() => {
            window.tweetMonitor = {
                seenTweetIds: new Set(),
                currentUsername: null,
                lastScrollTime: Date.now(),

                init() {
                    console.log("🔧 Enhanced tweet monitor initialized");
                },

                setUsername(username) {
                    this.currentUsername = username;
                    console.log(`👀 Now monitoring @${username}`);
                },

                getAllTweets() {
                    // Multiple selectors for better compatibility
                    const selectors = [
                        'article[data-testid="tweet"]',
                        'div[data-testid="cellInnerDiv"] article',
                        '[data-testid="tweet"]'
                    ];

                    for (const selector of selectors) {
                        const elements = document.querySelectorAll(selector);
                        if (elements.length > 0) {
                            return Array.from(elements);
                        }
                    }
                    return [];
                },

                extractTweetData(tweetElement) {
                    try {
                        // Get tweet URL and ID
                        const linkEl = tweetElement.querySelector('a[href*="/status/"]');
                        if (!linkEl) return null;

                        const url = linkEl.href;
                        const idMatch = url.match(/\\/status\\/(\\d+)/);
                        if (!idMatch) return null;
                        const id = idMatch[1];

                        // Get username from URL or link
                        const usernameMatch = url.match(/twitter\\.com\\/([^/]+)\\/status/);
                        const username = usernameMatch ? usernameMatch[1] : this.currentUsername;

                        // Get tweet text
                        let text = "";
                        const textEl = tweetElement.querySelector('div[data-testid="tweetText"]');
                        if (textEl) {
                            text = textEl.innerText.trim();
                        } else {
                            // Fallback text extraction
                            const possibleTextElements = tweetElement.querySelectorAll('div[lang], span[lang]');
                            for (const el of possibleTextElements) {
                                if (el.innerText && el.innerText.trim().length > text.length) {
                                    text = el.innerText.trim();
                                }
                            }
                        }

                        // Get time information
                        let displayTime = "Unknown time";
                        let timestamp = null;
                        const timeEl = tweetElement.querySelector('time');
                        if (timeEl) {
                            displayTime = timeEl.innerText;
                            timestamp = timeEl.getAttribute('datetime');
                        }

                        // Get profile image
                        let profileImage = null;
                        const profileImgEl = tweetElement.querySelector('img[src*="profile_images"]');
                        if (profileImgEl) {
                            profileImage = profileImgEl.src;
                        }

                        // Get tweet images/media
                        const mediaImages = [];
                        const mediaEls = tweetElement.querySelectorAll('img[src*="media"]');
                        mediaEls.forEach(img => {
                            if (img.src && img.src.includes('media')) {
                                // Get larger version of image
                                const largeUrl = img.src.replace(/&name=\\w+/, '&name=large');
                                mediaImages.push(largeUrl);
                            }
                        });

                        // Get engagement metrics
                        const getMetric = (testId) => {
                            const element = tweetElement.querySelector(`[data-testid="${testId}"]`);
                            if (!element) return "0";

                            const textEl = element.querySelector('[data-testid="app-text-transition-container"]') ||
                                          element.querySelector('span');
                            return textEl ? textEl.innerText.trim() || "0" : "0";
                        };

                        const replies = getMetric("reply");
                        const retweets = getMetric("retweet");
                        const likes = getMetric("like");
                        const views = getMetric("views");

                        // Check if this is a retweet
                        const isRetweet = tweetElement.querySelector('[data-testid="socialContext"]') !== null;

                        return {
                            id,
                            text: text || "No text content",
                            url,
                            time: displayTime,
                            timestamp,
                            username,
                            profileImage,
                            mediaImages,
                            replies,
                            retweets,
                            likes,
                            views,
                            isRetweet,
                            detectedAt: new Date().toISOString(),
                            isNew: !this.seenTweetIds.has(id)
                        };
                    } catch (error) {
                        console.error("Error extracting tweet data:", error);
                        return null;
                    }
                },

                getNewTweets() {
                    const tweetElements = this.getAllTweets();
                    const newTweets = [];

                    for (const tweetEl of tweetElements) {
                        const tweetData = this.extractTweetData(tweetEl);

                        if (!tweetData || !tweetData.id) continue;

                        // Only return truly new tweets
                        if (!this.seenTweetIds.has(tweetData.id)) {
                            this.seenTweetIds.add(tweetData.id);
                            newTweets.push(tweetData);
                        }
                    }

                    return newTweets;
                },

                processExistingTweets() {
                    const tweetElements = this.getAllTweets();
                    let count = 0;

                    for (const tweetEl of tweetElements) {
                        const tweetData = this.extractTweetData(tweetEl);
                        if (tweetData && tweetData.id) {
                            this.seenTweetIds.add(tweetData.id);
                            count++;
                        }
                    }

                    return count;
                },

                smartScroll() {
                    const now = Date.now();
                    if (now - this.lastScrollTime < 1000) return false;

                    const actions = [
                        () => window.scrollBy(0, Math.floor(200 + Math.random() * 400)), // Scroll down
                        () => window.scrollBy(0, -Math.floor(100 + Math.random() * 400)), // Scroll up
                        () => window.scrollTo(0, 0), // Go to top
                        () => window.scrollTo(0, Math.floor(window.innerHeight * 0.5)) // Go to middle
                    ];

                    const weights = [0.6, 0.2, 0.15, 0.05]; // Weighted random selection
                    const random = Math.random();
                    let cumulativeWeight = 0;

                    for (let i = 0; i < actions.length; i++) {
                        cumulativeWeight += weights[i];
                        if (random <= cumulativeWeight) {
                            actions[i]();
                            break;
                        }
                    }

                    this.lastScrollTime = now;
                    return true;
                }
            };

            window.tweetMonitor.init();
        }""")

        return True

    async def monitor_account(self, username):
        """Monitor a specific Twitter account by creating a dedicated page/tab"""
        print(f"🎯 Setting up monitoring for @{username}...")

        try:
            if self.context is None:
                print("❌ Browser context not initialized.")
                return False

            # Create a new page for this account
            account_page = await self.context.new_page()
            account_page.set_default_timeout(30000)

            # Navigate to the user's profile
            await account_page.goto(f"https://twitter.com/{username}", wait_until='networkidle')
            await asyncio.sleep(2)  # Wait for content to load

            # Check if profile exists
            not_found = await account_page.query_selector('text="This account doesn\'t exist"')
            if not_found:
                print(f"❌ Account @{username} doesn\'t exist or is suspended")
                await account_page.close()
                return False

            # Inject the monitoring JavaScript on this page
            await self.inject_enhanced_monitor_on_page(account_page)

            # Set the current username in the monitor
            await account_page.evaluate(f"window.tweetMonitor.setUsername('{username}')")

            # Process existing tweets as baseline
            baseline_count = await account_page.evaluate("window.tweetMonitor.processExistingTweets()")
            print(f"📊 Found {baseline_count} existing tweets as baseline for @{username}")

            # Scroll to top to catch newest tweets
            await account_page.evaluate("window.scrollTo(0, 0)")
            await asyncio.sleep(1)

            # Store account info with page reference
            self.monitored_accounts[username] = {
                'page': account_page,
                'start_time': datetime.now(),
                'tweet_count': 0
            }

            print(f"✅ Successfully set up monitoring for @{username}")
            return True

        except Exception as e:
            print(f"❌ Error monitoring account @{username}: {e}")
            return False

    async def check_for_new_tweets(self):
        """Check for new tweets on all monitored account pages"""
        all_new_tweets = []

        for username, account_info in self.monitored_accounts.items():
            page = account_info['page']
            try:
                # Smart scrolling
                await page.evaluate("window.tweetMonitor.smartScroll()")
                await asyncio.sleep(0.2)  # Brief pause for content to load

                # Get new tweets
                new_tweets = await page.evaluate("window.tweetMonitor.getNewTweets()")
                if new_tweets:
                    # Add username to each tweet for identification
                    for tweet in new_tweets:
                        tweet['monitored_username'] = username
                    all_new_tweets.extend(new_tweets)

            except Exception as e:
                print(f"⚠️ Error checking for new tweets on @{username}: {e}")
                continue

        return all_new_tweets

    async def process_tweet_media(self, tweet):
        """Process tweet media URLs (no local download needed)"""
        try:
            # Clean up media URLs for better quality
            if tweet.get('mediaImages'):
                cleaned_media = []
                for url in tweet['mediaImages']:
                    # Get higher quality version
                    clean_url = url.replace('&name=small', '&name=large').replace('&name=medium', '&name=large')
                    cleaned_media.append(clean_url)
                tweet['mediaImages'] = cleaned_media

            return tweet

        except Exception as e:
            print(f"⚠️ Error processing media for tweet {tweet.get('id', 'unknown')}: {e}")
            return tweet



    async def start_monitoring(self):
        """Start the main monitoring loop (legacy method for backward compatibility)"""
        self.is_running = True

        if not await self.setup_browser():
            print("❌ Failed to set up browser or login. Please check your setup.")
            return

        await self.inject_enhanced_monitor()

        # Start the monitoring loop
        await self.start_monitoring_loop()

    async def start_monitoring_loop(self):
        """Start the main monitoring loop (assumes browser is already set up)"""
        self.is_running = True

        cycle_count = 0
        status_interval = 20  # Print status every 20 cycles

        print("\n🚀 Starting enhanced monitoring loop...")
        print(f"📊 Monitoring {len(self.monitored_accounts)} accounts simultaneously")

        try:
            while self.is_running:
                # Check for new tweets on all accounts
                new_tweets = await self.check_for_new_tweets()

                # Process new tweets
                if new_tweets:
                    await self.process_new_tweets(new_tweets)

                # Print status periodically
                if cycle_count % status_interval == 0:
                    try:
                        total_seen = 0
                        accounts_info = []
                        for username, info in self.monitored_accounts.items():
                            # Get seen count for this account
                            try:
                                page = info['page']
                                seen_count = await page.evaluate("window.tweetMonitor.seenTweetIds.size")
                                total_seen += seen_count
                            except:
                                seen_count = 0
                            accounts_info.append(f"@{username}({info['tweet_count']})")

                        status = f"📊 Monitoring: {' | '.join(accounts_info)} | Total seen: {total_seen} tweets"
                        print(status)
                    except Exception as e:
                        print(f"⚠️ Error getting status: {e}")

                # Adaptive delay based on activity
                delay = 1.0 if new_tweets else 2.0
                await asyncio.sleep(delay)
                cycle_count += 1

        except KeyboardInterrupt:
            print("\n⏹️ Monitoring stopped by user")
        except Exception as e:
            print(f"❌ Error during monitoring: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self.is_running = False

    async def stop_monitoring(self):
        """Stop monitoring and cleanup"""
        print("🛑 Stopping monitor...")
        self.is_running = False

        # Close all account pages
        for username, account_info in self.monitored_accounts.items():
            try:
                page = account_info['page']
                await page.close()
                print(f"✅ Closed page for @{username}")
            except Exception as e:
                print(f"⚠️ Error closing page for @{username}: {e}")

        # Close main page and browser
        if self.page:
            await self.page.close()
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()

        print("✅ Monitor stopped and resources cleaned up")


# Enhanced callback functions
def enhanced_console_callback(tweet_data):
    """Enhanced console callback with better formatting"""
    print(f"\n⚡ NEW TWEET DETECTED ⚡")
    print(f"👤 @{tweet_data['username']}")
    print(f"📝 {tweet_data['text'][:200]}{'...' if len(tweet_data['text']) > 200 else ''}")
    print(f"🔗 {tweet_data['url']}")
    print(f"⏰ {tweet_data['time']}")

    if tweet_data.get('isRetweet'):
        print("🔄 This is a retweet")

    # Show engagement metrics
    metrics = []
    if tweet_data.get('replies', '0') != '0':
        metrics.append(f"💬 {tweet_data['replies']}")
    if tweet_data.get('retweets', '0') != '0':
        metrics.append(f"🔄 {tweet_data['retweets']}")
    if tweet_data.get('likes', '0') != '0':
        metrics.append(f"❤️ {tweet_data['likes']}")
    if tweet_data.get('views', '0') != '0':
        metrics.append(f"👁️ {tweet_data['views']}")

    if metrics:
        print(f"📊 {' | '.join(metrics)}")

    # Show media info
    if tweet_data.get('mediaImages'):
        print(f"🖼️ Contains {len(tweet_data['mediaImages'])} image(s)")

    print("=" * 80)


async def enhanced_discord_webhook_callback(tweet_data):
    """Enhanced Discord webhook with image support"""
    DISCORD_WEBHOOK_URL = os.getenv("DISCORD_WEBHOOK_URL")
    if not DISCORD_WEBHOOK_URL:
        print("❌ Discord webhook URL not configured")
        return

    try:
        # Create embed similar to the example image
        embed = {
            "author": {
                "name": f"{tweet_data['username']} (@{tweet_data['username']})",
                "icon_url": tweet_data.get('profileImage', ''),
                "url": f"https://twitter.com/{tweet_data['username']}"
            },
            "description": tweet_data['text'][:4000],  # Discord limit
            "color": 0x1DA1F2,
            "url": tweet_data['url'],
            "timestamp": datetime.now().isoformat(),
            "footer": {
                "text": f"Twitter • {tweet_data['time']}",
                "icon_url": "https://abs.twimg.com/icons/apple-touch-icon-192x192.png"
            }
        }

        # Add engagement metrics if available
        metrics = []
        if tweet_data.get('replies', '0') != '0':
            metrics.append(f"💬 {tweet_data['replies']}")
        if tweet_data.get('retweets', '0') != '0':
            metrics.append(f"🔄 {tweet_data['retweets']}")
        if tweet_data.get('likes', '0') != '0':
            metrics.append(f"❤️ {tweet_data['likes']}")

        if metrics:
            embed["fields"] = [{
                "name": "📊 Engagement",
                "value": " | ".join(metrics),
                "inline": True
            }]

        # Add image if available
        if tweet_data.get('mediaImages'):
            embed["image"] = {"url": tweet_data['mediaImages'][0]}

        # Prepare the webhook payload
        payload = {
            "embeds": [embed],
            "username": "Twitter Monitor",
            "avatar_url": "https://abs.twimg.com/icons/apple-touch-icon-192x192.png"
        }

        # Add retweet indicator
        if tweet_data.get('isRetweet'):
            payload["content"] = f"🔄 **Retweet from @{tweet_data['username']}**"
        else:
            payload["content"] = f"🐦 **New tweet from @{tweet_data['username']}**"

        # Send to Discord
        timeout = aiohttp.ClientTimeout(total=10)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(DISCORD_WEBHOOK_URL, json=payload) as response:
                if response.status in [200, 204]:
                    print(f"✅ Sent to Discord: @{tweet_data['username']}")
                else:
                    error_text = await response.text()
                    print(f"❌ Discord error {response.status}: {error_text}")

    except Exception as e:
        print(f"❌ Discord webhook error: {e}")


# Configuration management
def load_config():
    """Load monitoring configuration"""
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r') as f:
                return json.load(f)
        except:
            pass
    return {"accounts": [], "settings": {}}

def save_config(config):
    """Save monitoring configuration"""
    try:
        with open(CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=2)
    except Exception as e:
        print(f"⚠️ Error saving config: {e}")


async def main():
    print("""
⚡ Enhanced Twitter Monitor v2.0
==================================
🚀 Advanced Tweet detection with image support
🖼️ Profile and media image downloading
📨 Enhanced Discord webhook notifications
🔄 Robust multi-account monitoring
⚙️ Better error handling and recovery

Set DISCORD_WEBHOOK_URL environment variable for notifications.
    """)

    # Load existing configuration
    config = load_config()

    # Get accounts to monitor
    if config.get("accounts"):
        print(f"📋 Using saved accounts: {', '.join(['@' + a for a in config['accounts']])}")
        accounts = config["accounts"]
    else:
        accounts_input = input("Enter Twitter usernames to monitor (comma-separated, no @): ").strip()
        if not accounts_input:
            print("No accounts specified. Using 'elonmusk' as demo")
            accounts = ["elonmusk"]
        else:
            accounts = [username.strip().lstrip('@') for username in accounts_input.split(',')]

    # Save configuration
    config["accounts"] = accounts
    save_config(config)

    print(f"🎯 Will monitor: {', '.join(['@' + a for a in accounts])}")

    # Create and configure monitor
    monitor = TwitterMonitor()
    monitor.add_callback(enhanced_console_callback)
    monitor.add_callback(enhanced_discord_webhook_callback)

    # Set up browser FIRST before trying to monitor accounts
    print("\n🚀 Setting up browser...")
    if not await monitor.setup_browser():
        print("❌ Failed to set up browser. Please check your setup.")
        return

    # Check login status
    if not await monitor.check_login_status():
        print("❌ Not logged in to Twitter. Please log in manually in the browser window.")
        print("   After logging in, restart the program.")
        await monitor.stop_monitoring()
        return

    # Inject the monitoring JavaScript
    await monitor.inject_enhanced_monitor()

    # Now set up monitoring for each account
    print("\n🔄 Setting up account monitoring...")
    for username in accounts:
        success = await monitor.monitor_account(username)
        if not success:
            print(f"⚠️ Could not set up monitoring for @{username}")

    if not monitor.monitored_accounts:
        print("❌ No accounts successfully configured for monitoring")
        await monitor.stop_monitoring()
        return

    # Start the monitoring loop
    try:
        await monitor.start_monitoring_loop()
    except KeyboardInterrupt:
        print("\n⏹️ Monitoring stopped by user")
    finally:
        await monitor.stop_monitoring()
        print("\nPress Enter to exit...")
        input()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Program terminated by user")
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        import traceback
        traceback.print_exc()
